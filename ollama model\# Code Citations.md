# Code Citations

## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.default
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.default
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "es
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "es
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.default
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.default
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esben
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esben
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-v
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-v
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
        
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
        
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
        "editor.defaultFormatter
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
        "editor.defaultFormatter
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
        "editor.defaultFormatter": "esbenp
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
        "editor.defaultFormatter": "esbenp
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
```


## License: unknown
https://github.com/soyof/Note/blob/8339d56323997b72f91504d51bd65c8d7ffee2cf/vscodeSetting.js

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "
```


## License: unknown
https://github.com/yangzhouQS/blog-demo/blob/400f8560f53d02b4777f4f3d059cdf1daf4a6e3c/%E6%A1%86%E6%9E%B6%E6%BA%90%E7%A0%81/%40mctech/kaka-grid/README.md

```
html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!An
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!An
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!Import
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!Import
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!Import
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select",
        "!
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select",
        "!
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select",
        "!
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select",
        "!Select sequence",
        
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select",
        "!Select sequence",
        
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select",
        "!Select sequence",
        
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select",
        "!Select sequence",
        "!Split",
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select",
        "!Select sequence",
        "!Split",
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select",
        "!Select sequence",
        "!Split",
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select",
        "!Select sequence",
        "!Split",
        "!Split sequence
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select",
        "!Select sequence",
        "!Split",
        "!Split sequence
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select",
        "!Select sequence",
        "!Split",
        "!Split sequence
```


## License: unknown
https://github.com/radiosilence/dotfiles/blob/95ea4d295d9161ec9b90d91653bf72112f7272c7/dotfiles.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select",
        "!Select sequence",
        "!Split",
        "!Split sequence"
```


## License: unknown
https://github.com/mjwalf/dotfiles/blob/d503080afd81ce3abb21ca32300773befaef865d/vscode/extensions/kddejong.vscode-cfn-lint-0.10.8/client/src/extension.ts

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select",
        "!Select sequence",
        "!Split",
        "!Split sequence"
```


## License: MIT
https://github.com/nhs-england-tools/make-devops/blob/fc8e7fe303b6a5f0a7e13015208d09fc59825c2a/project.code-workspace

```
,
    "yaml.customTags": [
        "!And",
        "!And sequence",
        "!If",
        "!If sequence",
        "!Not",
        "!Not sequence",
        "!Equals",
        "!Equals sequence",
        "!Or",
        "!Or sequence",
        "!FindInMap",
        "!FindInMap sequence",
        "!Base64",
        "!Join",
        "!Join sequence",
        "!Cidr",
        "!Ref",
        "!Sub",
        "!Sub sequence",
        "!GetAtt",
        "!GetAZs",
        "!ImportValue",
        "!ImportValue sequence",
        "!Select",
        "!Select sequence",
        "!Split",
        "!Split sequence"
```

